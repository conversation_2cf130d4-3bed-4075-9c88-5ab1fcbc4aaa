import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { Calendar, User, CreditCard, ShoppingBag, Package, Info, Eye, Search, X, Filter, CalendarIcon, Plus, DollarSign, RefreshCw } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { formatCurrency, formatDate } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format, startOfMonth, endOfMonth, subMonths, isWithinInterval, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";
import PaymentDialog from "@/components/admin/PaymentDialog";

interface OrderManagementProps {
  initialStatusFilter?: string | null;
  initialPaymentStatusFilter?: string | null;
}

export function OrderManagement({ initialStatusFilter, initialPaymentStatusFilter }: OrderManagementProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isMobile, setIsMobile] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);

  // Estados para filtros
  const [searchId, setSearchId] = useState("");
  const [searchCustomer, setSearchCustomer] = useState("");
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [filterPaymentStatus, setFilterPaymentStatus] = useState<string | null>(null);

  // useEffect para aplicar os filtros iniciais quando o componente montar
  useEffect(() => {
    if (initialStatusFilter) {
      setFilterStatus(initialStatusFilter);
    }
  }, [initialStatusFilter]);

  useEffect(() => {
    if (initialPaymentStatusFilter) {
      // Processar múltiplos valores separados por vírgula
      const paymentStatuses = initialPaymentStatusFilter.split(',');

      // Mapear valores do URL para valores do banco
      const statusMapping: { [key: string]: string } = {
        'pending': 'pendente',
        'partial': 'parcialmente_recebido',
        'received': 'recebido'
      };

      // Mapear todos os status
      const mappedStatuses = paymentStatuses.map(status => statusMapping[status] || status);

      // Para múltiplos status, vamos usar uma string especial que será processada no filtro
      if (mappedStatuses.length > 1) {
        setFilterPaymentStatus(`multiple:${mappedStatuses.join(',')}`);
        console.log('Setting multiple payment status filter:', mappedStatuses, 'from URL:', initialPaymentStatusFilter);
      } else {
        setFilterPaymentStatus(mappedStatuses[0]);
        console.log('Setting single payment status filter:', mappedStatuses[0], 'from URL:', initialPaymentStatusFilter);
      }
    }
  }, [initialPaymentStatusFilter]);
  const [dateFilterType, setDateFilterType] = useState<"none" | "month" | "specific">("none");
  const [selectedMonth, setSelectedMonth] = useState<number>(0); // 0 = mês atual, -1 = mês anterior, etc.
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState<"start" | "end">("start");
  const [selectedOrderForPayment, setSelectedOrderForPayment] = useState<any>(null);
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);

  // Detectar se é mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Verificar inicialmente
    checkIfMobile();

    // Adicionar listener para redimensionamento
    window.addEventListener('resize', checkIfMobile);

    // Forçar verificação após um curto período
    const timer = setTimeout(checkIfMobile, 500);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkIfMobile);
      clearTimeout(timer);
    };
  }, []);

  // Fetch orders
  const { data: orders, isLoading } = useQuery({
    queryKey: ['/api/orders'],
    select: (data) => {
      console.log('🔄 Processando dados dos pedidos no select:', data);

      // Processar os pedidos para incluir informações sobre revisões
      return data.map((order: any) => {
        console.log(`📦 Processando pedido #${order.id}:`, {
          statusOriginal: order.status,
          paymentStatusOriginal: order.paymentStatus,
          hasCurrentRevision: order.hasCurrentRevision,
          currentRevisionId: order.currentRevisionId
        });

        // Manter sempre o status original do pedido para exibição na lista
        const originalStatus = order.status;
        const originalPaymentStatus = order.paymentStatus;

        // Se o pedido já tem dados de revisão processados pelo backend
        if (order.hasCurrentRevision && order.currentRevisionId) {
          console.log(`✅ Pedido #${order.id} já processado no backend com revisão`);
          return {
            ...order,
            // Manter sempre o status original para exibição na lista
            status: originalStatus,
            paymentStatus: originalPaymentStatus,
            hasRevision: true,
            hasCurrentRevision: true,
            revisionNumber: order.revisionNumber || 1
          };
        }

        // Se o pedido tem revisões no formato antigo, processar
        if (order.revisions && order.revisions.length > 0) {
          // Encontrar a revisão atual (isCurrent = true) ou a mais recente
          const currentRevision = order.revisions.find((rev: any) => rev.isCurrent) ||
                                 order.revisions.sort((a: any, b: any) =>
                                   b.revisionNumber - a.revisionNumber)[0];

          console.log(`🔄 Processando revisões do pedido #${order.id}:`, {
            currentRevision: currentRevision,
            statusRevisao: currentRevision.status,
            paymentStatusRevisao: currentRevision.paymentStatus,
            statusOriginalMantido: originalStatus
          });

          // Mesclar os dados da revisão com o pedido original, mas manter o status original
          return {
            ...order,
            // MANTER SEMPRE o status original do pedido para exibição na lista
            status: originalStatus,
            paymentStatus: originalPaymentStatus,
            // Sobrescrever outros campos que podem ser alterados na revisão
            receivingMethod: currentRevision.receivingMethod || order.receivingMethod,
            receivingDate: currentRevision.receivingDate || order.receivingDate,
            receivingTime: currentRevision.receivingTime || order.receivingTime,
            deliveryAddress: currentRevision.deliveryAddress || order.deliveryAddress,
            paymentMethod: currentRevision.paymentMethod || order.paymentMethod,
            subtotal: currentRevision.subtotal || order.subtotal,
            deliveryFee: currentRevision.deliveryFee || order.deliveryFee,
            total: currentRevision.total || order.total,
            notes: currentRevision.notes || order.notes,
            // Adicionar flag para indicar que este pedido tem revisão
            hasRevision: true,
            hasCurrentRevision: true,
            revisionNumber: currentRevision.revisionNumber || 1,
            currentRevision
          };
        }

        // Se não tem revisões, retornar o pedido original
        console.log(`📋 Pedido #${order.id} sem revisões`);
        return {
          ...order,
          hasRevision: false,
          hasCurrentRevision: false
        };
      });
    }
  });

  // Estado para controlar os popovers de data
  const [startDatePopoverOpen, setStartDatePopoverOpen] = useState(false);
  const [endDatePopoverOpen, setEndDatePopoverOpen] = useState(false);

  // Efeito para adicionar um listener para o evento personalizado de fechamento do popover
  useEffect(() => {
    // Função para fechar o popover usando a API do Radix UI
    const handleClosePopover = (event: CustomEvent) => {
      // Verifica se o evento tem um tipo específico
      const popoverType = event.detail?.type;

      if (popoverType === 'startDate') {
        setStartDatePopoverOpen(false);
      } else if (popoverType === 'endDate') {
        setEndDatePopoverOpen(false);
      } else {
        // Fallback para o comportamento anterior
        const openPopovers = document.querySelectorAll('[data-state="open"][data-radix-popover-trigger]');
        openPopovers.forEach((popover) => {
          // Simula um clique para fechar o popover
          (popover as HTMLElement).click();
        });
      }
    };

    // Adiciona o listener para o evento personalizado
    document.addEventListener('close-popover', handleClosePopover as EventListener);

    // Remove o listener quando o componente é desmontado
    return () => {
      document.removeEventListener('close-popover', handleClosePopover as EventListener);
    };
  }, []);

  // Função para limpar todos os filtros
  const clearAllFilters = () => {
    setSearchId("");
    setSearchCustomer("");
    setFilterStatus(null);
    setFilterPaymentStatus(null);
    setDateFilterType("none");
    setSelectedMonth(0);
    setStartDate(undefined);
    setEndDate(undefined);
  };

  // Função para obter o intervalo de datas do mês selecionado
  const getSelectedMonthRange = () => {
    const today = new Date();
    const targetMonth = subMonths(today, Math.abs(selectedMonth));
    return {
      start: startOfMonth(targetMonth),
      end: endOfMonth(targetMonth)
    };
  };

  // Função para verificar se um pedido está dentro do intervalo de datas
  const isOrderInDateRange = (order: any) => {
    if (dateFilterType === "none") return true;

    const orderDate = parseISO(order.createdAt);

    if (dateFilterType === "month") {
      const { start, end } = getSelectedMonthRange();
      return isWithinInterval(orderDate, { start, end });
    }

    if (dateFilterType === "specific") {
      // Se temos data de início e fim, verificamos se está no intervalo
      if (startDate && endDate) {
        // Ajustamos a data de fim para incluir todo o dia
        const adjustedEndDate = new Date(endDate);
        adjustedEndDate.setHours(23, 59, 59, 999);

        return isWithinInterval(orderDate, {
          start: startDate,
          end: adjustedEndDate
        });
      }
      // Se temos apenas data de início, verificamos se é igual ou posterior
      else if (startDate) {
        const orderDateString = format(orderDate, "yyyy-MM-dd");
        const startDateString = format(startDate, "yyyy-MM-dd");
        return orderDateString >= startDateString;
      }
      // Se temos apenas data de fim, verificamos se é igual ou anterior
      else if (endDate) {
        const orderDateString = format(orderDate, "yyyy-MM-dd");
        const endDateString = format(endDate, "yyyy-MM-dd");
        return orderDateString <= endDateString;
      }
    }

    return true;
  };

  // Filtrar pedidos com base nos critérios de busca
  const filteredOrders = orders
    ? orders.filter((order: any) => {
        // Filtro por ID
        const matchesId = searchId === "" ||
          order.id.toString().includes(searchId);

        // Filtro por cliente
        const matchesCustomer = searchCustomer === "" ||
          (order.customer?.name && order.customer.name.toLowerCase().includes(searchCustomer.toLowerCase()));

        // Filtro por status
        const matchesStatus = filterStatus === null ||
          order.status === filterStatus;

        // Filtro por status de pagamento
        let matchesPaymentStatus = true;
        if (filterPaymentStatus !== null) {
          if (filterPaymentStatus.startsWith('multiple:')) {
            // Filtro múltiplo - verificar se o status do pedido está na lista
            const allowedStatuses = filterPaymentStatus.replace('multiple:', '').split(',');
            matchesPaymentStatus = allowedStatuses.includes(order.paymentStatus);

            // Debug do filtro múltiplo
            console.log(`[PAYMENT FILTER MULTIPLE] Order ${order.id}: paymentStatus="${order.paymentStatus}", allowedStatuses=[${allowedStatuses.join(', ')}], matches=${matchesPaymentStatus}`);
          } else {
            // Filtro simples
            matchesPaymentStatus = order.paymentStatus === filterPaymentStatus;

            // Debug do filtro simples
            console.log(`[PAYMENT FILTER SINGLE] Order ${order.id}: paymentStatus="${order.paymentStatus}", filter="${filterPaymentStatus}", matches=${matchesPaymentStatus}`);
          }
        }

        // Filtro por data
        const matchesDate = isOrderInDateRange(order);

        return matchesId && matchesCustomer && matchesStatus && matchesPaymentStatus && matchesDate;
      })
    : [];

  // Update order status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }: { id: number; status: string }) =>
      apiRequest('PATCH', `/api/orders/${id}/status`, { status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/orders'] });
      toast({
        title: t('orders.statusUpdated'),
        variant: "success",
      });
    },
    onError: (error) => {
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive",
      });
    }
  });

  // Importando useLocation do wouter
  const [, setLocation] = useLocation();

  // Navegar para a página de detalhes do pedido
  const handleViewDetails = (orderId: number) => {
    setLocation(`/admin/orders/${orderId}`);
  };

  // Handle status change
  const handleStatusChange = (status: string) => {
    if (selectedOrder) {
      updateStatusMutation.mutate({ id: selectedOrder.id, status });
      // Also update the selected order status for UI
      setSelectedOrder({ ...selectedOrder, status });
    }
  };

  // Render status badge
  const OrderStatusBadge = ({ status }: { status: string }) => {
    let variant: "default" | "secondary" | "destructive" | "outline" = "default";

    switch (status) {
      case "delivered":
        variant = "default";
        break;
      case "pending":
        variant = "secondary";
        break;
      case "confirmed":
        variant = "secondary";
        break;
      case "cancelled":
        variant = "destructive";
        break;
      // Support legacy statuses temporarily
      case "completed":
      case "processing":
        variant = "secondary";
        break;
      default:
        variant = "outline";
    }

    return (
      <Badge variant={variant}>
        {t(`dashboard.${status}`)}
      </Badge>
    );
  };

  // Payment Status Badge Component
  const PaymentStatusBadge = ({ paymentStatus }: { paymentStatus: string }) => {
    let variant: "default" | "secondary" | "destructive" | "outline" = "outline";
    let className = "";

    switch (paymentStatus) {
      case "recebido":
        variant = "default";
        className = "bg-green-100 text-green-800 border-green-200";
        break;
      case "parcialmente_recebido":
        variant = "secondary";
        className = "bg-yellow-100 text-yellow-800 border-yellow-200";
        break;
      case "pendente":
        variant = "destructive";
        className = "bg-red-100 text-red-800 border-red-200";
        break;
      default:
        variant = "outline";
        className = "bg-gray-100 text-gray-800 border-gray-200";
    }

    const statusText = {
      recebido: "Recebido",
      parcialmente_recebido: "Parcial",
      pendente: "Pendente"
    }[paymentStatus] || paymentStatus;

    return (
      <Badge variant={variant} className={className}>
        {statusText}
      </Badge>
    );
  };

  // Quick Payment Registration Handler
  const handleQuickPayment = (order: any) => {
    setSelectedOrderForPayment(order);
    setIsPaymentDialogOpen(true);
  };

  // Renderiza um card para cada pedido (versão mobile)
  const renderOrderCard = (order: any) => (
    <div
      key={order.id}
      className="bg-white rounded-lg shadow p-4 mb-4"
      onClick={() => handleViewDetails(order.id)}
    >
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center">
          <span className="font-medium">
            <strong>#{order.id}</strong> • {order.customer?.name || 'Unknown'}
          </span>
          {order.hasCurrentRevision && (
            <Badge variant="outline" className="ml-2 text-xs bg-blue-50 border-blue-200 text-blue-600">
              Rev.
            </Badge>
          )}
        </div>
        <div className="flex flex-col gap-1">
          <OrderStatusBadge status={order.status} />
          <PaymentStatusBadge paymentStatus={order.paymentStatus} />
        </div>
      </div>

      <div className="flex justify-between items-center mb-3">
        <div className="flex flex-col text-sm">
          <div className="flex items-center text-muted-foreground">
            <Calendar className="h-4 w-4 mr-1" />
            {order.receivingDate ? formatDate(order.receivingDate) : formatDate(order.createdAt)}
          </div>
          <div className="text-xs text-muted-foreground mt-1">
            {order.receivingDate ? '' : 'Data do pedido'}
          </div>
        </div>
        <div className="text-right">
          <div className="font-medium text-primary">
            {formatCurrency(order.total)}
          </div>
          {order.hasCurrentRevision && (
            <div className="text-xs text-muted-foreground">
              Valor da revisão
            </div>
          )}
        </div>
      </div>

      {order.hasCurrentRevision && order.revisionNumber > 1 && (
            <div className="mb-3 text-xs text-muted-foreground bg-blue-50 p-2 rounded">
              <div className="flex items-center">
                <Info className="h-3 w-3 mr-1 text-blue-500" />
              </div>
            </div>
          )}

      <div className="flex justify-end items-center">
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center"
          onClick={(e) => {
            e.stopPropagation();
            handleViewDetails(order.id);
          }}
        >
          <Eye className="h-4 w-4 mr-1" />
          {t('orders.viewDetails')}
        </Button>
      </div>
    </div>
  );

  // Renderiza skeletons para carregamento (versão mobile)
  const renderMobileLoadingSkeletons = () => (
    Array(3).fill(0).map((_, index) => (
      <div key={index} className="bg-white rounded-lg shadow p-4 mb-4">
        <div className="flex justify-between items-center mb-2">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-5 w-20" />
        </div>
        <div className="flex justify-between items-center mb-3">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-16" />
        </div>
        <div className="flex justify-end">
          <Skeleton className="h-8 w-24" />
        </div>
      </div>
    ))
  );

  // Função para renderizar o componente de filtro de data
  const renderDateFilter = () => {
    return (
      <div className="space-y-4">
        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium">{t('orders.dateFilterType')}</label>
          <Select
            value={dateFilterType}
            onValueChange={(value: "none" | "month" | "specific") => {
              setDateFilterType(value);
              if (value === "none") {
                setSelectedMonth(0);
                setStartDate(undefined);
                setEndDate(undefined);
              }
            }}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder={t('orders.selectDateFilterType')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">{t('orders.noDateFilter')}</SelectItem>
              <SelectItem value="month">{t('orders.monthlyFilter')}</SelectItem>
              <SelectItem value="specific">{t('orders.specificDateFilter')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {dateFilterType === "month" && (
          <div className="flex flex-col space-y-2">
            <label className="text-sm font-medium">{t('orders.selectMonth')}</label>
            <Select
              value={selectedMonth.toString()}
              onValueChange={(value) => setSelectedMonth(parseInt(value))}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t('orders.selectMonth')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">{t('orders.currentMonth')}</SelectItem>
                <SelectItem value="-1">{t('orders.previousMonth')}</SelectItem>
                <SelectItem value="-2">{t('orders.twoMonthsAgo')}</SelectItem>
                <SelectItem value="-3">{t('orders.threeMonthsAgo')}</SelectItem>
              </SelectContent>
            </Select>
            {selectedMonth !== null && (
              <div className="text-xs text-muted-foreground mt-1">
                {format(getSelectedMonthRange().start, "MMMM yyyy", { locale: ptBR })}
              </div>
            )}
          </div>
        )}

        {dateFilterType === "specific" && (
          <div className="flex flex-col space-y-4">
            {/* Data de início */}
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium">{t('orders.startDate')}</label>
              <Popover open={startDatePopoverOpen} onOpenChange={setStartDatePopoverOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? (
                      format(startDate, "PPP", { locale: ptBR })
                    ) : (
                      <span>{t('orders.pickStartDate')}</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start" sideOffset={4}>
                  <CalendarComponent
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => {
                      setStartDate(date);
                      // Se a data de fim for anterior à data de início, ajustamos
                      if (date && endDate && date > endDate) {
                        setEndDate(date);
                      }
                      // Fechar o popover diretamente após a seleção
                      if (date) {
                        setStartDatePopoverOpen(false);
                      }
                    }}
                    initialFocus
                    disabled={(date) => {
                      // Não permitir datas futuras
                      return date > new Date();
                    }}
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Data de fim */}
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium">{t('orders.endDate')}</label>
              <Popover open={endDatePopoverOpen} onOpenChange={setEndDatePopoverOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? (
                      format(endDate, "PPP", { locale: ptBR })
                    ) : (
                      <span>{t('orders.pickEndDate')}</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start" sideOffset={4}>
                  <CalendarComponent
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => {
                      setEndDate(date);
                      // Fechar o popover diretamente após a seleção
                      if (date) {
                        setEndDatePopoverOpen(false);
                      }
                    }}
                    initialFocus
                    disabled={(date) => {
                      // Não permitir datas futuras ou anteriores à data de início
                      return date > new Date() || (startDate && date < startDate);
                    }}
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Resumo do intervalo selecionado */}
            {startDate && endDate && (
              <div className="text-xs text-muted-foreground">
                {t('orders.dateRangeSummary', {
                  start: format(startDate, "dd/MM/yyyy"),
                  end: format(endDate, "dd/MM/yyyy")
                })}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // Função para navegar para a página de novo pedido
  const handleNewOrder = () => {
    setLocation('/admin/orders/new');
  };

  // Função para atualizar a lista de pedidos
  const handleRefreshOrders = () => {
    queryClient.invalidateQueries({ queryKey: ['/api/orders'] });
    toast({
      title: t('common.success') || "Sucesso",
      description: t('orders.dataRefreshed') || "Lista de pedidos atualizada com sucesso.",
      variant: "default",
    });
  };

  return (
    <div>
      {/* Cabeçalho com botões de ação */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">{t('orders.ordersList') || "Lista de Pedidos"}</h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefreshOrders}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {t('common.refresh') || "Atualizar"}
          </Button>
          <Button onClick={handleNewOrder}>
            <Plus className="h-4 w-4 mr-2" />
            {t('orders.newOrder') || "Novo Pedido"}
          </Button>
        </div>
      </div>

      {/* Filtros */}
      <div className="mb-6 space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Busca por ID */}
          <div className="relative w-full sm:w-40">
            <Input
              placeholder={`${t('orders.searchById')}...`}
              value={searchId}
              onChange={(e) => setSearchId(e.target.value)}
              className="pl-10"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            {searchId && (
              <button
                onClick={() => setSearchId("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>

          {/* Busca por Cliente */}
          <div className="relative w-full sm:w-60">
            <Input
              placeholder={`${t('orders.searchByCustomer')}...`}
              value={searchCustomer}
              onChange={(e) => setSearchCustomer(e.target.value)}
              className="pl-10"
            />
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            {searchCustomer && (
              <button
                onClick={() => setSearchCustomer("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>

          {/* Filtro por Status */}
          <div className="w-full sm:w-48">
            <Select
              value={filterStatus || "all"}
              onValueChange={(value) => setFilterStatus(value === "all" ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('orders.filterByStatus')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('orders.allStatuses')}</SelectItem>
                <SelectItem value="pending">{t('dashboard.pending')}</SelectItem>
                <SelectItem value="confirmed">{t('dashboard.confirmed')}</SelectItem>
                <SelectItem value="delivered">{t('dashboard.delivered')}</SelectItem>
                <SelectItem value="cancelled">{t('dashboard.cancelled')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Status de Pagamento */}
          <div className="w-full sm:w-48">
            <Select
              value={filterPaymentStatus || "all"}
              onValueChange={(value) => setFilterPaymentStatus(value === "all" ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Status de Pagamento" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os Pagamentos</SelectItem>
                <SelectItem value="pendente">Apenas Pendentes</SelectItem>
                <SelectItem value="parcialmente_recebido">Apenas Parciais</SelectItem>
                <SelectItem value="recebido">Apenas Recebidos</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Data (Desktop) */}
          {!isMobile && (
            <div className="w-full sm:w-48">
              <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-between">
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4" />
                      {dateFilterType === "none" ? (
                        t('orders.filterByDate')
                      ) : dateFilterType === "month" ? (
                        format(getSelectedMonthRange().start, "MMMM yyyy", { locale: ptBR })
                      ) : dateFilterType === "specific" && startDate && endDate ? (
                        `${format(startDate, "dd/MM")} - ${format(endDate, "dd/MM")}`
                      ) : dateFilterType === "specific" && startDate ? (
                        `${t('orders.from')} ${format(startDate, "dd/MM")}`
                      ) : dateFilterType === "specific" && endDate ? (
                        `${t('orders.until')} ${format(endDate, "dd/MM")}`
                      ) : (
                        t('orders.filterByDate')
                      )}
                    </div>
                    <Filter className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="space-y-4 p-2">
                    <h4 className="font-medium">{t('orders.dateFilters')}</h4>
                    {renderDateFilter()}
                    <div className="flex justify-between pt-2">
                      <Button variant="outline" size="sm" onClick={() => {
                        setDateFilterType("none");
                        setSelectedMonth(0);
                        setStartDate(undefined);
                        setEndDate(undefined);
                        setIsFilterOpen(false);
                      }}>
                        {t('common.clear')}
                      </Button>
                      <Button size="sm" onClick={() => setIsFilterOpen(false)}>
                        {t('common.apply')}
                      </Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          )}

          {/* Botão Limpar Filtros */}
          <Button
            variant="ghost"
            size="icon"
            onClick={clearAllFilters}
            title={t('orders.clearFilters')}
            className="hidden sm:flex"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Filtro por Data (Mobile) */}
        {isMobile && dateFilterType !== "none" && (
          <div className="bg-muted/30 p-3 rounded-md">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-sm font-medium">{t('orders.dateFilters')}</h4>
              <Button variant="ghost" size="sm" onClick={() => {
                setDateFilterType("none");
                setSelectedMonth(0);
                setStartDate(undefined);
                setEndDate(undefined);
              }}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            {dateFilterType === "month" && (
              <div className="text-sm">
                {format(getSelectedMonthRange().start, "MMMM yyyy", { locale: ptBR })}
              </div>
            )}
            {dateFilterType === "specific" && startDate && endDate && (
              <div className="text-sm">
                {format(startDate, "dd/MM/yyyy")} - {format(endDate, "dd/MM/yyyy")}
              </div>
            )}
            {dateFilterType === "specific" && startDate && !endDate && (
              <div className="text-sm">
                {t('orders.from')} {format(startDate, "dd/MM/yyyy")}
              </div>
            )}
            {dateFilterType === "specific" && !startDate && endDate && (
              <div className="text-sm">
                {t('orders.until')} {format(endDate, "dd/MM/yyyy")}
              </div>
            )}
          </div>
        )}

        {/* Filtro por Data (Mobile) - Botão */}
        {isMobile && (
          <div className="w-full">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-between">
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    {t('orders.filterByDate')}
                  </div>
                  <Filter className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="space-y-4 p-2">
                  <h4 className="font-medium">{t('orders.dateFilters')}</h4>
                  {renderDateFilter()}
                </div>
              </PopoverContent>
            </Popover>
          </div>
        )}

        {/* Botão Limpar Filtros (Mobile) */}
        {isMobile && (searchId || searchCustomer || filterStatus || dateFilterType !== "none") && (
          <Button
            variant="outline"
            size="sm"
            onClick={clearAllFilters}
            className="w-full"
          >
            <X className="h-4 w-4 mr-2" />
            {t('orders.clearFilters')}
          </Button>
        )}
      </div>

      {isMobile ? (
        // Mobile view - Cards
        <div className="space-y-4">
          {isLoading ? (
            renderMobileLoadingSkeletons()
          ) : filteredOrders.length > 0 ? (
            filteredOrders.map((order: any) => renderOrderCard(order))
          ) : (
            <div className="bg-white rounded-lg shadow p-6 text-center">
              <p className="text-muted-foreground">
                {(searchId || searchCustomer || filterStatus || dateFilterType !== "none")
                  ? t('orders.noOrdersMatchingFilters')
                  : t('orders.noOrders')}
              </p>
              {(searchId || searchCustomer || filterStatus || dateFilterType !== "none") && (
                <Button variant="outline" size="sm" onClick={clearAllFilters} className="mt-2">
                  {t('orders.clearFilters')}
                </Button>
              )}
            </div>
          )}
        </div>
      ) : (
        // Desktop view - Table
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px]">ID</TableHead>
                  <TableHead>{t('dashboard.customer')}</TableHead>
                  <TableHead>{t('dashboard.orderDate')}</TableHead>
                  <TableHead>{t('dashboard.orderValue')}</TableHead>
                  <TableHead>{t('dashboard.orderStatus')}</TableHead>
                  <TableHead>{t('orders.paymentStatus')}</TableHead>
                  <TableHead className="text-right">{t('common.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  // Loading skeletons
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-8" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-4 w-16 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredOrders.length > 0 ? (
                  filteredOrders.map((order: any) => (
                    <TableRow key={order.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          #{order.id}
                          {order.hasCurrentRevision && (
                            <Badge variant="outline" className="ml-2 text-xs bg-blue-50 border-blue-200 text-blue-600">
                              Rev.
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{order.customer?.name || 'Unknown'}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {order.receivingDate ? formatDate(order.receivingDate) : formatDate(order.createdAt)}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {order.receivingDate ? '' : 'Data do pedido'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {formatCurrency(order.total)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <OrderStatusBadge status={order.status} />
                      </TableCell>
                      <TableCell>
                        <PaymentStatusBadge paymentStatus={order.paymentStatus} />
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewDetails(order.id)}
                        >
                          {t('orders.viewDetails')}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6">
                      {(searchId || searchCustomer || filterStatus || filterPaymentStatus || dateFilterType !== "none")
                        ? (
                          <div className="space-y-2">
                            <p>{t('orders.noOrdersMatchingFilters')}</p>
                            <Button variant="outline" size="sm" onClick={clearAllFilters}>
                              {t('orders.clearFilters')}
                            </Button>
                          </div>
                        )
                        : t('orders.noOrders')
                      }
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Payment Dialog for Quick Payment Registration */}
      {selectedOrderForPayment && (
        <PaymentDialog
          open={isPaymentDialogOpen}
          onOpenChange={setIsPaymentDialogOpen}
          orderId={selectedOrderForPayment.id}
          orderTotal={selectedOrderForPayment.total}
          onPaymentAdded={() => {
            queryClient.invalidateQueries({ queryKey: ['/api/orders'] });
            setIsPaymentDialogOpen(false);
            setSelectedOrderForPayment(null);
          }}
        />
      )}
    </div>
  );
}

export default OrderManagement;