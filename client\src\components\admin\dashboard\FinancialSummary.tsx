import { Card } from "@/components/ui/card";
import { TrendingUp, TrendingDown, DollarSign, ShoppingBag, Clock } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useLocation } from "wouter";
import { Sparkline } from "./Sparkline";
import { cn } from "@/lib/utils";

interface FinancialSummaryProps {
  summary: {
    totalRevenue: number;
    currentMonthRevenue: number;
    monthlyGrowth: number;
    avgOrderValue: number;
    totalOrders: number;
    pendingPaymentAmount: number;
  };
  monthlyComparison: {
    current: number;
    previous: number;
    growth: number;
  };
  sparklineData?: {
    totalRevenue: Array<{ value: number; date: string }>;
    monthlyRevenue: Array<{ value: number; date: string }>;
    avgOrderValue: Array<{ value: number; date: string }>;
    totalOrders: Array<{ value: number; date: string }>;
    pendingPaymentAmount: Array<{ value: number; date: string }>;
  };
}

export function FinancialSummary({ summary, monthlyComparison, sparklineData }: FinancialSummaryProps) {
  const { t } = useTranslation();
  const [, setLocation] = useLocation();
  const formatCurrency = (value: number) => `R$ ${value.toFixed(2)}`;
  const formatPercentage = (value: number) => `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;

  // Generate sample sparkline data if not provided
  const defaultSparklineData = Array.from({ length: 7 }, (_, i) => ({
    value: Math.random() * 1000 + 500,
    date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString()
  }));

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Receita Total */}
      <Card className="p-5 bg-white/90 backdrop-blur-sm border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{t('dashboard.financialCards.totalRevenue')}</p>
            <p className="text-2xl font-bold text-gray-900">{formatCurrency(summary.totalRevenue)}</p>
          </div>
          <div className="p-2.5 bg-green-100 rounded-xl">
            <DollarSign className="h-5 w-5 text-green-600" />
          </div>
        </div>
        <div className="mt-3">
          <Sparkline
            data={sparklineData?.totalRevenue || defaultSparklineData}
            color="#10B981"
            trend="up"
            height={32}
            className="rounded-lg"
          />
        </div>
      </Card>

      {/* Receita do Mês */}
      <Card className="p-5 bg-white/90 backdrop-blur-sm border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{t('dashboard.financialCards.monthlyRevenue')}</p>
            <p className="text-2xl font-bold text-gray-900">{formatCurrency(summary.currentMonthRevenue)}</p>
            <div className="flex items-center gap-1 mt-2">
              {monthlyComparison.growth >= 0 ? (
                <TrendingUp className="h-3.5 w-3.5 text-green-600" />
              ) : (
                <TrendingDown className="h-3.5 w-3.5 text-red-600" />
              )}
              <span className={cn(
                "text-xs font-semibold",
                monthlyComparison.growth >= 0 ? 'text-green-600' : 'text-red-600'
              )}>
                {formatPercentage(monthlyComparison.growth)}
              </span>
              <span className="text-xs text-gray-500 ml-1">
                {monthlyComparison.growth >= 0 ? t('dashboard.financialCards.growth') : t('dashboard.financialCards.decline')}
              </span>
            </div>
          </div>
          <div className="p-2.5 bg-blue-100 rounded-xl">
            <TrendingUp className="h-5 w-5 text-blue-600" />
          </div>
        </div>
        <div className="mt-3">
          <Sparkline
            data={sparklineData?.monthlyRevenue || defaultSparklineData}
            color="#3B82F6"
            trend={monthlyComparison.growth >= 0 ? "up" : "down"}
            height={32}
            className="rounded-lg"
          />
        </div>
      </Card>

      {/* Valor Médio do Pedido */}
      <Card className="p-5 bg-white/90 backdrop-blur-sm border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{t('dashboard.financialCards.averageTicket')}</p>
            <p className="text-2xl font-bold text-gray-900">{formatCurrency(summary.avgOrderValue)}</p>
            <p className="text-xs text-gray-500 mt-1">{t('dashboard.financialCards.perPaidOrder')}</p>
          </div>
          <div className="p-2.5 bg-orange-100 rounded-xl">
            <ShoppingBag className="h-5 w-5 text-orange-600" />
          </div>
        </div>
        <div className="mt-3">
          <Sparkline
            data={sparklineData?.avgOrderValue || defaultSparklineData}
            color="#F59E0B"
            trend="neutral"
            height={32}
            className="rounded-lg"
          />
        </div>
      </Card>

      {/* Pedidos Pendentes */}
      <Card
        className="p-5 bg-white/90 backdrop-blur-sm border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 hover:scale-[1.02] cursor-pointer"
        onClick={() => setLocation('/admin/orders?paymentStatus=pending,partial')}
      >
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{t('dashboard.financialCards.pendingPaymentAmount')}</p>
            <p className="text-2xl font-bold text-gray-900">{formatCurrency(summary.pendingPaymentAmount)}</p>
            <p className="text-xs text-gray-500 mt-1">{t('dashboard.financialCards.pendingPayments')}</p>
          </div>
          <div className="p-2.5 bg-orange-100 rounded-xl">
            <Clock className="h-5 w-5 text-orange-600" />
          </div>
        </div>
        <div className="mt-3">
          <Sparkline
            data={sparklineData?.pendingPaymentAmount || defaultSparklineData}
            color="#F59E0B"
            trend="neutral"
            height={32}
            className="rounded-lg"
          />
        </div>
      </Card>
    </div>
  );
}