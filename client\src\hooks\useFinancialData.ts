import { useQuery } from "@tanstack/react-query";
import { PeriodValue } from "@/components/admin/dashboard/PeriodFilter";
import { apiRequest } from "@/lib/queryClient";

interface FinancialData {
  summary: {
    totalRevenue: number;
    currentMonthRevenue: number;
    monthlyGrowth: number;
    avgOrderValue: number;
    totalOrders: number;
    pendingPaymentOrders: number;
  };
  revenueChart: Array<{
    month: string;
    revenue: number;
    orders: number;
  }>;
  topProductsByRevenue: Array<{
    name: string;
    revenue: number;
    quantity: number;
    avgPrice: number;
  }>;
  monthlyComparison: {
    current: number;
    previous: number;
    growth: number;
  };
  sparklineData: {
    totalRevenue: Array<{ value: number; date: string }>;
    monthlyRevenue: Array<{ value: number; date: string }>;
    avgOrderValue: Array<{ value: number; date: string }>;
    totalOrders: Array<{ value: number; date: string }>;
    pendingPaymentOrders: Array<{ value: number; date: string }>;
  };
}

// As funções auxiliares foram movidas para o backend
// O endpoint /api/dashboard/financial agora calcula tudo baseado no período

export function useFinancialData(period: PeriodValue = "thisMonth") {
  return useQuery<FinancialData>({
    queryKey: ['/api/dashboard/financial', period],
    queryFn: async () => {
      // Usar apiRequest que já implementa autenticação correta
      const url = `/api/dashboard/financial?period=${period}`;

      try {
        const response = await apiRequest('GET', url);

        // Se response é um objeto Response, extrair JSON
        let financialData;
        if (response instanceof Response) {
          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to fetch financial data: ${response.status} ${errorText}`);
          }
          financialData = await response.json();
        } else {
          // Se já é o objeto de dados
          financialData = response;
        }

        return financialData;
      } catch (error) {
        throw error;
      }
    },
    refetchInterval: 60000, // 1 minute
    staleTime: 30000, // 30 seconds
  });
}
